# Cloudflare Radar MCP OAuth Setup Guide

## 🎯 Overview

This guide will walk you through completing the OAuth authentication flow to enable real-time Cloudflare Radar threat intelligence data in your BlackVeil Security platform.

## ✅ Prerequisites Completed

- ✅ **<PERSON>**: Installed at `/Applications/Claude.app`
- ✅ **MCP Configuration**: Updated at `~/Library/Application Support/Claude/claude_desktop_config.json`
- ✅ **Supabase Environment**: API token configured, ready for OAuth token

## 📋 Step-by-Step OAuth Process

### **Step 1: Launch Claude Desktop**

1. **Open Claude Desktop**:
   ```bash
   open /Applications/Claude.app
   ```
   
2. **Wait for MCP Server Connection**:
   - <PERSON> will automatically read the configuration
   - It will attempt to connect to the Cloudflare Radar MCP server
   - You should see MCP server status in the interface

### **Step 2: Trigger OAuth Flow**

1. **Automatic OAuth Trigger**:
   - When Claude Desktop connects to the MCP server, it will automatically detect that OAuth is required
   - A browser window should open automatically pointing to <PERSON><PERSON><PERSON>e's OAuth page
   - If it doesn't open automatically, look for an OAuth prompt in Claude Desktop

2. **Manual Trigger (if needed)**:
   - In <PERSON>, try asking: "What Cloudflare Radar tools are available?"
   - This should trigger the OAuth flow if it hasn't started automatically

### **Step 3: Complete Browser Authentication**

1. **Cloudflare Login Page**:
   - You'll be redirected to `https://dash.cloudflare.com/login` or similar
   - Log in with your Cloudflare account credentials
   - If you don't have a Cloudflare account, create one (it's free)

2. **OAuth Authorization**:
   - You'll see a page asking to authorize access to Cloudflare Radar API
   - The page will show permissions being requested:
     - Read access to Radar API data
     - Access to threat intelligence endpoints
   - Click **"Authorize"** or **"Allow"**

3. **Success Confirmation**:
   - You should see a success page saying "Authorization complete"
   - The browser may show "You can close this window"
   - Return to Claude Desktop

### **Step 4: Verify OAuth Success in Claude Desktop**

1. **Test MCP Tools**:
   In Claude Desktop, try these commands to verify OAuth is working:
   
   ```
   What are the current Layer 7 attack trends?
   ```
   
   ```
   Can you scan the URL https://example.com for threats?
   ```
   
   ```
   Show me recent traffic anomalies globally.
   ```

2. **Expected Response**:
   - Claude should now have access to real Cloudflare Radar data
   - You should see actual threat intelligence numbers, not error messages
   - The responses should include real-time data and analysis

### **Step 5: Extract OAuth Token**

The OAuth token is stored by Claude Desktop. We need to extract it for our server-side integration.

1. **Check Claude Desktop Logs**:
   ```bash
   # Check Claude Desktop logs for OAuth token
   tail -f ~/Library/Logs/Claude/main.log | grep -i oauth
   ```

2. **Check MCP Configuration Directory**:
   ```bash
   # Look for token storage files
   find ~/Library/Application\ Support/Claude -name "*token*" -o -name "*oauth*" -o -name "*auth*"
   ```

3. **Check Network Storage**:
   ```bash
   # Check for stored credentials
   ls -la ~/Library/Application\ Support/Claude/Local\ Storage/leveldb/
   ```

4. **Alternative: Check Browser Storage**:
   - Open Chrome/Safari Developer Tools
   - Go to Application > Local Storage
   - Look for `radar.mcp.cloudflare.com` entries
   - Look for tokens or authentication data

### **Step 6: Manual Token Extraction (if needed)**

If automatic extraction doesn't work, we can capture the token during the OAuth flow:

1. **Browser Developer Tools Method**:
   - Before starting OAuth, open browser Developer Tools (F12)
   - Go to Network tab
   - Complete the OAuth flow
   - Look for requests to `radar.mcp.cloudflare.com`
   - Check request headers for `Authorization: Bearer <token>`

2. **Proxy Method**:
   ```bash
   # Set up a simple proxy to capture the token
   # This is advanced - only if other methods fail
   ```

## 🔧 Configure OAuth Token in Supabase

Once you have the OAuth token, configure it in our Supabase environment:

### **Set the OAuth Token**:
```bash
# Replace YOUR_OAUTH_TOKEN with the actual token
npx supabase secrets set CLOUDFLARE_RADAR_OAUTH_TOKEN=YOUR_OAUTH_TOKEN
```

### **Verify the Secret**:
```bash
npx supabase secrets list
# Should show both CLOUDFLARE_API_TOKEN and CLOUDFLARE_RADAR_OAUTH_TOKEN
```

## 🧪 Test OAuth Integration

After setting the OAuth token, test our integration:

### **Test Real Data**:
```bash
# Test the integration with OAuth
node scripts/verify-real-data-integration.js
```

### **Expected Results**:
```json
{
  "dataSource": "cloudflare_radar_mcp_hybrid",
  "mcpMetadata": {
    "toolsUsed": ["get_l7_attack_data", "get_email_security_data", "get_email_routing_data"],
    "dataFreshness": "real-time",
    "fallbackMode": "oauth_enabled"
  },
  "phishing": {
    "total": [REAL_NUMBERS],
    "trend": [REAL_TREND]
  }
}
```

## 🚨 Troubleshooting

### **OAuth Flow Doesn't Start**:
1. Restart Claude Desktop
2. Check MCP configuration file syntax
3. Ensure `mcp-remote` is accessible via `npx`

### **Browser Doesn't Open**:
1. Check Claude Desktop permissions
2. Try manual URL: `https://radar.mcp.cloudflare.com/auth`
3. Check firewall/proxy settings

### **Authorization Fails**:
1. Verify Cloudflare account has Radar access
2. Try different browser
3. Clear browser cache and cookies

### **Token Not Found**:
1. Check Claude Desktop is using the correct configuration
2. Verify OAuth completed successfully
3. Try re-running the OAuth flow

## 📞 Support Commands

### **Reset MCP Configuration**:
```bash
# Backup current config
cp ~/Library/Application\ Support/Claude/claude_desktop_config.json ~/claude_config_backup.json

# Reset to default
echo '{"mcpServers":{}}' > ~/Library/Application\ Support/Claude/claude_desktop_config.json
```

### **Check MCP Status**:
```bash
# Test if mcp-remote is working
npx mcp-remote --help

# Test connection to Cloudflare MCP server
npx mcp-remote https://radar.mcp.cloudflare.com/sse --test
```

### **Monitor Integration**:
```bash
# Monitor our integration status
node scripts/monitor-mcp-integration.js
```

## 🎉 Success Indicators

You'll know OAuth is working when:

1. ✅ **Claude Desktop**: Shows real Cloudflare Radar data
2. ✅ **Our Integration**: Returns `dataSource: "cloudflare_radar_mcp_hybrid"`
3. ✅ **Real Numbers**: Actual threat intelligence instead of fallback data
4. ✅ **Enhanced Features**: Access to URL scanning, anomaly detection, etc.

---

**Ready to start?** Launch Claude Desktop and let's get your OAuth flow completed!
