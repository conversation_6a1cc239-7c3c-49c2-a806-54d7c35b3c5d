#!/usr/bin/env node

/**
 * BlackVeil Security - OAuth Token Extraction Helper
 *
 * This script helps locate and extract the OAuth token from Claude Desktop
 * after completing the Cloudflare Radar MCP OAuth flow.
 */

import fs from 'fs';
import path from 'path';
import os from 'os';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(color, prefix, message) {
  const timestamp = new Date().toLocaleTimeString();
  console.log(`${colors.cyan}[${timestamp}]${colors.reset} ${color}[${prefix}]${colors.reset} ${message}`);
}

function logSuccess(message) {
  log(colors.green, '✅ SUCCESS', message);
}

function logError(message) {
  log(colors.red, '❌ ERROR', message);
}

function logWarning(message) {
  log(colors.yellow, '⚠️  WARNING', message);
}

function logInfo(message) {
  log(colors.blue, 'ℹ️  INFO', message);
}

function logSearch(message) {
  log(colors.magenta, '🔍 SEARCH', message);
}

/**
 * Search for files containing potential OAuth tokens
 */
function searchForTokenFiles(directory, patterns = ['token', 'oauth', 'auth', 'credential']) {
  const results = [];
  
  try {
    if (!fs.existsSync(directory)) {
      return results;
    }

    const items = fs.readdirSync(directory, { withFileTypes: true });
    
    for (const item of items) {
      const fullPath = path.join(directory, item.name);
      
      if (item.isDirectory()) {
        // Recursively search subdirectories
        results.push(...searchForTokenFiles(fullPath, patterns));
      } else if (item.isFile()) {
        // Check if filename contains any of our patterns
        const fileName = item.name.toLowerCase();
        if (patterns.some(pattern => fileName.includes(pattern))) {
          results.push(fullPath);
        }
      }
    }
  } catch (error) {
    // Silently ignore permission errors
  }
  
  return results;
}

/**
 * Search for OAuth tokens in file contents
 */
function searchFileContents(filePath, tokenPatterns = [
  /bearer\s+([a-zA-Z0-9_-]+)/i,
  /token["\s]*[:=]["\s]*([a-zA-Z0-9_-]+)/i,
  /oauth["\s]*[:=]["\s]*([a-zA-Z0-9_-]+)/i,
  /access_token["\s]*[:=]["\s]*([a-zA-Z0-9_-]+)/i
]) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const tokens = [];
    
    for (const pattern of tokenPatterns) {
      const matches = content.match(pattern);
      if (matches && matches[1] && matches[1].length > 10) {
        tokens.push({
          token: matches[1],
          pattern: pattern.toString(),
          file: filePath
        });
      }
    }
    
    return tokens;
  } catch (error) {
    return [];
  }
}

/**
 * Check Claude Desktop configuration and storage
 */
function checkClaudeDesktopStorage() {
  const homeDir = os.homedir();
  const claudeDir = path.join(homeDir, 'Library', 'Application Support', 'Claude');
  
  logSearch('Searching Claude Desktop storage for OAuth tokens...');
  
  const searchLocations = [
    claudeDir,
    path.join(claudeDir, 'Local Storage'),
    path.join(claudeDir, 'Session Storage'),
    path.join(claudeDir, 'IndexedDB'),
    path.join(claudeDir, 'blob_storage')
  ];
  
  const foundTokens = [];
  
  for (const location of searchLocations) {
    logInfo(`Searching: ${location}`);
    
    // Search for token-related files
    const tokenFiles = searchForTokenFiles(location);
    
    if (tokenFiles.length > 0) {
      logInfo(`Found ${tokenFiles.length} potential token files`);
      
      for (const file of tokenFiles) {
        logInfo(`  - ${file}`);
        
        // Search file contents for tokens
        const tokens = searchFileContents(file);
        foundTokens.push(...tokens);
      }
    }
  }
  
  return foundTokens;
}

/**
 * Check browser storage locations
 */
function checkBrowserStorage() {
  const homeDir = os.homedir();
  
  logSearch('Searching browser storage for OAuth tokens...');
  
  const browserLocations = [
    // Chrome
    path.join(homeDir, 'Library', 'Application Support', 'Google', 'Chrome', 'Default', 'Local Storage'),
    // Safari
    path.join(homeDir, 'Library', 'Safari', 'LocalStorage'),
    // Edge
    path.join(homeDir, 'Library', 'Application Support', 'Microsoft Edge', 'Default', 'Local Storage')
  ];
  
  const foundTokens = [];
  
  for (const location of browserLocations) {
    if (fs.existsSync(location)) {
      logInfo(`Searching browser storage: ${location}`);
      
      const tokenFiles = searchForTokenFiles(location);
      for (const file of tokenFiles) {
        if (file.includes('radar.mcp.cloudflare.com') || file.includes('cloudflare')) {
          logInfo(`  - Found Cloudflare-related file: ${file}`);
          const tokens = searchFileContents(file);
          foundTokens.push(...tokens);
        }
      }
    }
  }
  
  return foundTokens;
}

/**
 * Check system logs for OAuth tokens
 */
function checkSystemLogs() {
  const homeDir = os.homedir();
  
  logSearch('Searching system logs for OAuth activity...');
  
  const logLocations = [
    path.join(homeDir, 'Library', 'Logs', 'Claude'),
    '/var/log',
    path.join(homeDir, 'Library', 'Logs')
  ];
  
  const foundTokens = [];
  
  for (const location of logLocations) {
    if (fs.existsSync(location)) {
      logInfo(`Searching logs: ${location}`);
      
      try {
        const logFiles = fs.readdirSync(location)
          .filter(file => file.includes('claude') || file.includes('mcp') || file.includes('oauth'))
          .map(file => path.join(location, file));
        
        for (const file of logFiles) {
          logInfo(`  - Checking log: ${file}`);
          const tokens = searchFileContents(file);
          foundTokens.push(...tokens);
        }
      } catch (error) {
        // Ignore permission errors
      }
    }
  }
  
  return foundTokens;
}

/**
 * Validate potential OAuth tokens
 */
function validateTokens(tokens) {
  const validTokens = [];
  
  for (const tokenInfo of tokens) {
    const token = tokenInfo.token;
    
    // Basic validation
    if (token.length < 20) {
      continue; // Too short to be a real OAuth token
    }
    
    if (token.length > 500) {
      continue; // Too long to be a typical OAuth token
    }
    
    // Check for common OAuth token patterns
    if (/^[a-zA-Z0-9_-]+$/.test(token)) {
      validTokens.push(tokenInfo);
    }
  }
  
  return validTokens;
}

/**
 * Main token extraction function
 */
async function extractOAuthToken() {
  console.log('\n' + '='.repeat(70));
  console.log('🔍 BlackVeil Security - OAuth Token Extraction');
  console.log('='.repeat(70));

  logInfo('Starting OAuth token extraction process...');
  logInfo('This will search for Cloudflare Radar OAuth tokens in various locations');

  const allTokens = [];

  // Search Claude Desktop storage
  const claudeTokens = checkClaudeDesktopStorage();
  allTokens.push(...claudeTokens);

  // Search browser storage
  const browserTokens = checkBrowserStorage();
  allTokens.push(...browserTokens);

  // Search system logs
  const logTokens = checkSystemLogs();
  allTokens.push(...logTokens);

  // Validate and deduplicate tokens
  const validTokens = validateTokens(allTokens);
  const uniqueTokens = [...new Map(validTokens.map(t => [t.token, t])).values()];

  console.log('\n📊 Search Results:');
  console.log('='.repeat(50));

  if (uniqueTokens.length === 0) {
    logWarning('No OAuth tokens found automatically');
    
    console.log('\n🔧 Manual Extraction Steps:');
    console.log('='.repeat(50));
    logInfo('1. Complete OAuth flow in Claude Desktop first');
    logInfo('2. Try asking Claude: "What Cloudflare Radar tools are available?"');
    logInfo('3. Check browser Developer Tools during OAuth flow');
    logInfo('4. Look for Authorization headers in Network tab');
    
    console.log('\n📋 Alternative Methods:');
    console.log('='.repeat(50));
    logInfo('• Check Claude Desktop settings/preferences');
    logInfo('• Look for MCP server connection status');
    logInfo('• Monitor network traffic during OAuth');
    
  } else {
    logSuccess(`Found ${uniqueTokens.length} potential OAuth token(s)!`);
    
    console.log('\n🎯 Potential OAuth Tokens:');
    console.log('='.repeat(50));
    
    for (let i = 0; i < uniqueTokens.length; i++) {
      const tokenInfo = uniqueTokens[i];
      console.log(`\n${i + 1}. Token: ${tokenInfo.token.substring(0, 20)}...`);
      console.log(`   Source: ${tokenInfo.file}`);
      console.log(`   Pattern: ${tokenInfo.pattern}`);
      
      if (tokenInfo.token.length > 50) {
        logSuccess('This looks like a valid OAuth token!');
        
        console.log('\n🔧 Configure in Supabase:');
        console.log(`npx supabase secrets set CLOUDFLARE_RADAR_OAUTH_TOKEN=${tokenInfo.token}`);
      }
    }
  }

  console.log('\n' + '='.repeat(70));
  logInfo('OAuth token extraction completed');
  
  if (uniqueTokens.length > 0) {
    logSuccess('Found potential tokens - configure the most promising one in Supabase');
  } else {
    logWarning('No tokens found - complete OAuth flow in Claude Desktop first');
  }
  
  console.log('='.repeat(70));
}

// Run extraction
extractOAuthToken().catch(console.error);
