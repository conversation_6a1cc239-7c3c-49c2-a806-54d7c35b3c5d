#!/usr/bin/env node

/**
 * BlackVeil Security - MCP Integration Monitor
 *
 * This script continuously monitors the MCP integration status
 * and alerts when real data becomes available.
 */

import https from 'https';
import { performance } from 'perf_hooks';

// Configuration
const FUNCTION_URL = 'https://wikngnwwakatokbgvenw.supabase.co/functions/v1/cloudflare-radar-stats';
const CHECK_INTERVAL_MS = 30000; // 30 seconds
const MAX_CHECKS = 20; // 10 minutes total
const TIMEOUT_MS = 30000;

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(color, prefix, message) {
  const timestamp = new Date().toLocaleTimeString();
  console.log(`${colors.cyan}[${timestamp}]${colors.reset} ${color}[${prefix}]${colors.reset} ${message}`);
}

function logSuccess(message) {
  log(colors.green, '✅ SUCCESS', message);
}

function logError(message) {
  log(colors.red, '❌ ERROR', message);
}

function logWarning(message) {
  log(colors.yellow, '⚠️  WARNING', message);
}

function logInfo(message) {
  log(colors.blue, 'ℹ️  INFO', message);
}

function logMonitor(message) {
  log(colors.magenta, '👁️  MONITOR', message);
}

/**
 * Make HTTP request with timeout
 */
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error(`Request timeout after ${TIMEOUT_MS}ms`));
    }, TIMEOUT_MS);

    const req = https.request(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    }, (res) => {
      clearTimeout(timeout);
      
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data });
        }
      });
    });

    req.on('error', (error) => {
      clearTimeout(timeout);
      reject(error);
    });

    req.end();
  });
}

/**
 * Check integration status
 */
async function checkIntegrationStatus() {
  try {
    const startTime = performance.now();
    const response = await makeRequest(FUNCTION_URL);
    const endTime = performance.now();
    const responseTime = Math.round(endTime - startTime);

    if (response.status !== 200) {
      logError(`HTTP ${response.status}: ${response.data}`);
      return { success: false, isRealData: false };
    }

    const data = response.data;
    const isRealData = data.dataSource === 'cloudflare_radar_api' &&
                      (data.phishing?.total > 0 || data.spoofing?.total > 0);

    // Log current status
    if (isRealData) {
      logSuccess(`Real data active! Source: ${data.dataSource}`);
      logSuccess(`Response time: ${responseTime}ms`);

      if (data.phishing?.total) {
        logInfo(`Phishing attacks: ${data.phishing.total.toLocaleString()}`);
      }

      if (data.spoofing?.total) {
        logInfo(`Spoofing incidents: ${data.spoofing.total.toLocaleString()}`);
      }

      if (data.dmarc?.adoptionRate) {
        logInfo(`DMARC adoption: ${data.dmarc.adoptionRate.toFixed(1)}%`);
      }
      
    } else {
      logWarning(`Still using fallback data: ${data.dataSource}`);
      logInfo(`Response time: ${responseTime}ms`);
      
      if (data.error) {
        logWarning(`Error: ${data.error}`);
      }
    }

    return { success: true, isRealData, data, responseTime };

  } catch (error) {
    logError(`Check failed: ${error.message}`);
    return { success: false, isRealData: false, error };
  }
}

/**
 * Main monitoring function
 */
async function monitorIntegration() {
  console.log('\n' + '='.repeat(70));
  console.log('👁️  BlackVeil Security - MCP Integration Monitor');
  console.log('='.repeat(70));
  
  logMonitor('Starting continuous monitoring...');
  logInfo(`Checking every ${CHECK_INTERVAL_MS / 1000} seconds`);
  logInfo(`Maximum ${MAX_CHECKS} checks (${(MAX_CHECKS * CHECK_INTERVAL_MS) / 60000} minutes)`);
  logInfo('Press Ctrl+C to stop monitoring');
  
  console.log('\n📊 Status Updates:');
  console.log('='.repeat(50));

  let checkCount = 0;
  let realDataDetected = false;

  while (checkCount < MAX_CHECKS && !realDataDetected) {
    checkCount++;
    
    logMonitor(`Check ${checkCount}/${MAX_CHECKS}`);
    
    const result = await checkIntegrationStatus();
    
    if (result.success && result.isRealData) {
      realDataDetected = true;
      
      console.log('\n' + '🎉'.repeat(35));
      logSuccess('REAL DATA INTEGRATION DETECTED!');
      logSuccess('Direct API integration is now working with live Cloudflare Radar data!');
      console.log('🎉'.repeat(35));

      // Show final status
      console.log('\n📋 Final Integration Status:');
      console.log('='.repeat(50));
      logSuccess(`✅ Data Source: ${result.data.dataSource}`);
      logSuccess(`✅ Response Time: ${result.responseTime}ms`);
      logSuccess(`✅ Last Updated: ${result.data.lastUpdated}`);
      logSuccess(`✅ Integration Type: Direct API`);
      logSuccess(`✅ Authentication: API Token`);
      
      break;
    }
    
    if (checkCount < MAX_CHECKS && !realDataDetected) {
      logInfo(`Waiting ${CHECK_INTERVAL_MS / 1000} seconds for next check...`);
      await new Promise(resolve => setTimeout(resolve, CHECK_INTERVAL_MS));
    }
  }

  if (!realDataDetected) {
    console.log('\n' + '⚠️'.repeat(35));
    logWarning('MONITORING TIMEOUT');
    logWarning('Real data integration not detected within monitoring period');
    console.log('⚠️'.repeat(35));
    
    console.log('\n🔧 Troubleshooting Steps:');
    console.log('='.repeat(50));
    logInfo('1. Verify API token is set: supabase secrets list');
    logInfo('2. Check token value: supabase secrets set CLOUDFLARE_API_TOKEN=****************************************');
    logInfo('3. Clear cache: Make a few requests to force cache refresh');
    logInfo('4. Check function logs: supabase functions logs cloudflare-radar-stats');
    logInfo('5. Run verification script: node scripts/verify-real-data-integration.js');
  }

  logMonitor('Monitoring completed');
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n\n👋 Monitoring stopped by user');
  process.exit(0);
});

// Run monitoring
monitorIntegration().catch(console.error);
