#!/usr/bin/env node

/**
 * BlackVeil Security - OAuth Integration Test Script
 *
 * This script tests the OAuth-enabled MCP integration to verify
 * we're getting real Cloudflare Radar data through the MCP server.
 */

import https from 'https';
import { performance } from 'perf_hooks';

// Configuration
const FUNCTION_URL = 'https://wikngnwwakatokbgvenw.supabase.co/functions/v1/cloudflare-radar-stats';
const TIMEOUT_MS = 60000;

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(color, prefix, message) {
  const timestamp = new Date().toLocaleTimeString();
  console.log(`${colors.cyan}[${timestamp}]${colors.reset} ${color}[${prefix}]${colors.reset} ${message}`);
}

function logSuccess(message) {
  log(colors.green, '✅ SUCCESS', message);
}

function logError(message) {
  log(colors.red, '❌ ERROR', message);
}

function logWarning(message) {
  log(colors.yellow, '⚠️  WARNING', message);
}

function logInfo(message) {
  log(colors.blue, 'ℹ️  INFO', message);
}

function logTest(message) {
  log(colors.cyan, '🧪 TEST', message);
}

/**
 * Make HTTP request with timeout
 */
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error(`Request timeout after ${TIMEOUT_MS}ms`));
    }, TIMEOUT_MS);

    const req = https.request(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    }, (res) => {
      clearTimeout(timeout);
      
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data });
        }
      });
    });

    req.on('error', (error) => {
      clearTimeout(timeout);
      reject(error);
    });

    req.end();
  });
}

/**
 * Test OAuth integration status
 */
function testOAuthIntegration(data) {
  const tests = [];
  
  // Test 1: Data source indicates OAuth
  if (data.dataSource === 'cloudflare_radar_mcp_hybrid') {
    tests.push({ name: 'Data Source', status: 'PASS', message: 'MCP hybrid mode active' });
  } else {
    tests.push({ name: 'Data Source', status: 'FAIL', message: `Expected 'cloudflare_radar_mcp_hybrid', got '${data.dataSource}'` });
  }
  
  // Test 2: MCP metadata present
  if (data.mcpMetadata) {
    tests.push({ name: 'MCP Metadata', status: 'PASS', message: 'MCP metadata present' });
    
    // Test 2a: OAuth authentication method
    if (data.mcpMetadata.authenticationMethod === 'oauth_token') {
      tests.push({ name: 'OAuth Auth', status: 'PASS', message: 'OAuth authentication detected' });
    } else {
      tests.push({ name: 'OAuth Auth', status: 'WARN', message: `Auth method: ${data.mcpMetadata.authenticationMethod || 'unknown'}` });
    }
    
    // Test 2b: Fallback mode
    if (data.mcpMetadata.fallbackMode === 'oauth_enabled') {
      tests.push({ name: 'OAuth Mode', status: 'PASS', message: 'OAuth mode enabled' });
    } else {
      tests.push({ name: 'OAuth Mode', status: 'WARN', message: `Fallback mode: ${data.mcpMetadata.fallbackMode}` });
    }
    
    // Test 2c: Tools used
    if (data.mcpMetadata.toolsUsed && data.mcpMetadata.toolsUsed.length > 0) {
      tests.push({ name: 'MCP Tools', status: 'PASS', message: `${data.mcpMetadata.toolsUsed.length} tools used` });
    } else {
      tests.push({ name: 'MCP Tools', status: 'FAIL', message: 'No MCP tools detected' });
    }
    
  } else {
    tests.push({ name: 'MCP Metadata', status: 'FAIL', message: 'MCP metadata missing' });
  }
  
  // Test 3: Real data indicators
  const hasRealData = (data.phishing?.total > 0) || (data.spoofing?.total > 0) || (data.dmarc?.adoptionRate > 0);
  if (hasRealData) {
    tests.push({ name: 'Real Data', status: 'PASS', message: 'Real threat intelligence detected' });
  } else {
    tests.push({ name: 'Real Data', status: 'FAIL', message: 'No real data detected (all zeros)' });
  }
  
  // Test 4: Data freshness
  const lastUpdated = new Date(data.lastUpdated);
  const now = new Date();
  const ageMinutes = (now - lastUpdated) / (1000 * 60);
  
  if (ageMinutes < 60) {
    tests.push({ name: 'Data Freshness', status: 'PASS', message: `${Math.round(ageMinutes)} minutes old` });
  } else if (ageMinutes < 240) { // 4 hours
    tests.push({ name: 'Data Freshness', status: 'WARN', message: `${Math.round(ageMinutes)} minutes old (cached)` });
  } else {
    tests.push({ name: 'Data Freshness', status: 'FAIL', message: `${Math.round(ageMinutes)} minutes old (stale)` });
  }
  
  return tests;
}

/**
 * Display OAuth capabilities
 */
function displayOAuthCapabilities(data) {
  console.log('\n🚀 OAuth-Enabled Capabilities:');
  console.log('='.repeat(50));
  
  if (data.mcpMetadata?.authenticationMethod === 'oauth_token') {
    logSuccess('OAuth authentication active - Enhanced features available:');
    
    const enhancedFeatures = [
      '• scan_url - Real-time URL threat scanning',
      '• get_traffic_anomalies - Network anomaly detection',
      '• get_ip_details - IP reputation analysis',
      '• get_domains_ranking - Domain intelligence',
      '• get_as_details - ASN information',
      '• get_internet_quality_data - Performance metrics',
      '• get_ai_data - AI traffic analysis',
      '• list_autonomous_systems - Network forensics',
      '• Enhanced email security analysis',
      '• Advanced attack attribution'
    ];
    
    enhancedFeatures.forEach(feature => logInfo(feature));
    
  } else {
    logWarning('OAuth not detected - Limited to basic API features');
    logInfo('Complete OAuth setup to unlock 18+ MCP tools');
  }
}

/**
 * Main OAuth test function
 */
async function testOAuthIntegration() {
  console.log('\n' + '='.repeat(70));
  console.log('🔐 BlackVeil Security - OAuth Integration Test');
  console.log('='.repeat(70));

  logInfo('Testing OAuth-enabled Cloudflare Radar MCP integration...');

  try {
    // Fetch current data
    logTest('Fetching integration data...');
    const startTime = performance.now();
    
    const response = await makeRequest(FUNCTION_URL);
    const endTime = performance.now();
    const responseTime = Math.round(endTime - startTime);

    if (response.status !== 200) {
      logError(`HTTP error: ${response.status}`);
      return;
    }

    logSuccess(`Data fetched successfully (${responseTime}ms)`);

    // Run OAuth integration tests
    logTest('Running OAuth integration tests...');
    const tests = testOAuthIntegration(response.data);

    console.log('\n📊 OAuth Integration Test Results:');
    console.log('='.repeat(50));

    let passCount = 0;
    let warnCount = 0;
    let failCount = 0;

    for (const test of tests) {
      switch (test.status) {
        case 'PASS':
          logSuccess(`${test.name}: ${test.message}`);
          passCount++;
          break;
        case 'WARN':
          logWarning(`${test.name}: ${test.message}`);
          warnCount++;
          break;
        case 'FAIL':
          logError(`${test.name}: ${test.message}`);
          failCount++;
          break;
      }
    }

    // Display OAuth capabilities
    displayOAuthCapabilities(response.data);

    // Show data summary
    console.log('\n📈 Current Data Summary:');
    console.log('='.repeat(50));
    logInfo(`Data Source: ${response.data.dataSource}`);
    logInfo(`Last Updated: ${response.data.lastUpdated}`);
    logInfo(`Response Time: ${responseTime}ms`);
    
    if (response.data.phishing) {
      logInfo(`Phishing Attacks: ${response.data.phishing.total?.toLocaleString() || 'N/A'}`);
    }
    
    if (response.data.spoofing) {
      logInfo(`Spoofing Incidents: ${response.data.spoofing.total?.toLocaleString() || 'N/A'}`);
    }
    
    if (response.data.dmarc) {
      logInfo(`DMARC Adoption: ${response.data.dmarc.adoptionRate?.toFixed(1) || 'N/A'}%`);
    }

    // Final assessment
    console.log('\n' + '='.repeat(70));
    console.log('🎯 OAuth Integration Assessment');
    console.log('='.repeat(70));

    const totalTests = tests.length;
    logInfo(`Tests: ${passCount} passed, ${warnCount} warnings, ${failCount} failed (${totalTests} total)`);

    if (failCount === 0 && response.data.mcpMetadata?.authenticationMethod === 'oauth_token') {
      logSuccess('🎉 OAuth integration is working perfectly!');
      logSuccess('🎉 Real-time Cloudflare Radar threat intelligence is active');
      logSuccess('🎉 Enhanced MCP features are available');
    } else if (failCount === 0) {
      logWarning('⚠️  Integration working but OAuth may not be fully configured');
      logInfo('ℹ️  Complete OAuth setup for enhanced features');
    } else {
      logError('❌ OAuth integration issues detected');
      logInfo('ℹ️  Check OAuth token configuration and MCP server connectivity');
    }

  } catch (error) {
    logError(`OAuth integration test failed: ${error.message}`);
    console.error(error);
  }
}

// Run OAuth integration test
testOAuthIntegration().catch(console.error);
