#!/usr/bin/env node

/**
 * BlackVeil Security - Real Data Integration Verification Script
 *
 * This script specifically tests that the MCP integration is working with
 * real Cloudflare Radar API data and not fallback data.
 */

import https from 'https';
import { performance } from 'perf_hooks';

// Configuration
const FUNCTION_URL = 'https://wikngnwwakatokbgvenw.supabase.co/functions/v1/cloudflare-radar-stats';
const TIMEOUT_MS = 60000;

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(color, prefix, message) {
  console.log(`${color}[${prefix}]${colors.reset} ${message}`);
}

function logSuccess(message) {
  log(colors.green, '✅ SUCCESS', message);
}

function logError(message) {
  log(colors.red, '❌ ERROR', message);
}

function logWarning(message) {
  log(colors.yellow, '⚠️  WARNING', message);
}

function logInfo(message) {
  log(colors.blue, 'ℹ️  INFO', message);
}

function logTest(message) {
  log(colors.cyan, '🧪 TEST', message);
}

/**
 * Make HTTP request with timeout
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error(`Request timeout after ${TIMEOUT_MS}ms`));
    }, TIMEOUT_MS);

    const req = https.request(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    }, (res) => {
      clearTimeout(timeout);
      
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({ status: res.statusCode, data: parsed, headers: res.headers });
        } catch (e) {
          resolve({ status: res.statusCode, data, headers: res.headers });
        }
      });
    });

    req.on('error', (error) => {
      clearTimeout(timeout);
      reject(error);
    });

    req.end();
  });
}

/**
 * Verify real data characteristics
 */
function verifyRealData(data) {
  const realDataIndicators = [];
  const issues = [];

  // Check data source
  if (data.dataSource === 'cloudflare_radar_api') {
    realDataIndicators.push('✅ Data source indicates direct API integration');
  } else {
    issues.push(`❌ Expected 'cloudflare_radar_api', got '${data.dataSource}'`);
  }

  // Check for direct API integration
  if (data.dataSource === 'cloudflare_radar_api') {
    realDataIndicators.push('✅ Direct API integration active');
    realDataIndicators.push('✅ Using real Cloudflare Radar data');
  } else {
    issues.push(`❌ Not using real API data (source: ${data.dataSource})`);
  }

  // Check for non-zero data (indicates real API responses)
  if (data.phishing && data.phishing.total > 0) {
    realDataIndicators.push(`✅ Phishing data: ${data.phishing.total.toLocaleString()} attacks`);
  } else {
    issues.push('❌ Phishing data is zero or missing');
  }

  if (data.spoofing && data.spoofing.total > 0) {
    realDataIndicators.push(`✅ Spoofing data: ${data.spoofing.total.toLocaleString()} incidents`);
  } else {
    issues.push('❌ Spoofing data is zero or missing');
  }

  if (data.dmarc && data.dmarc.adoptionRate > 0) {
    realDataIndicators.push(`✅ DMARC adoption: ${data.dmarc.adoptionRate.toFixed(1)}%`);
  } else {
    issues.push('❌ DMARC data is zero or missing');
  }

  // Check industry risks
  if (data.industryRisks && Object.keys(data.industryRisks).length > 0) {
    const riskCount = Object.keys(data.industryRisks).length;
    realDataIndicators.push(`✅ Industry risks: ${riskCount} industries analyzed`);
  } else {
    issues.push('❌ Industry risks data is missing');
  }

  return { realDataIndicators, issues };
}

/**
 * Main verification function
 */
async function verifyRealDataIntegration() {
  console.log('\n' + '='.repeat(70));
  console.log('🛡️  BlackVeil Security - Real Data Integration Verification');
  console.log('='.repeat(70));

  logInfo('Verifying Cloudflare Radar MCP integration with real data...');
  logInfo(`Function URL: ${FUNCTION_URL}`);

  try {
    // Test 1: Fetch data
    logTest('Fetching current data...');
    const startTime = performance.now();
    
    const response = await makeRequest(FUNCTION_URL);
    const endTime = performance.now();
    const responseTime = Math.round(endTime - startTime);

    if (response.status !== 200) {
      logError(`HTTP error: ${response.status}`);
      console.log('Response:', response.data);
      return;
    }

    logSuccess(`Data fetched successfully (${responseTime}ms)`);

    // Test 2: Verify real data characteristics
    logTest('Analyzing data characteristics...');
    const { realDataIndicators, issues } = verifyRealData(response.data);

    console.log('\n📊 Real Data Analysis:');
    console.log('='.repeat(50));

    if (realDataIndicators.length > 0) {
      console.log('\n🎯 Real Data Indicators:');
      realDataIndicators.forEach(indicator => console.log(`  ${indicator}`));
    }

    if (issues.length > 0) {
      console.log('\n⚠️  Issues Found:');
      issues.forEach(issue => console.log(`  ${issue}`));
    }

    // Test 3: Configuration status
    logTest('Checking configuration status...');
    
    const isRealData = response.data.dataSource === 'cloudflare_radar_mcp_hybrid' && 
                      response.data.mcpMetadata && 
                      (response.data.phishing?.total > 0 || response.data.spoofing?.total > 0);

    console.log('\n🔧 Configuration Status:');
    console.log('='.repeat(50));

    if (isRealData) {
      logSuccess('✅ REAL DATA INTEGRATION ACTIVE');
      logSuccess('✅ Cloudflare API token is properly configured');
      logSuccess('✅ MCP integration is working with live data');
      
      if (response.data.mcpMetadata?.fallbackMode === 'direct_api') {
        logInfo('ℹ️  Currently using direct API fallback (as designed)');
        logInfo('ℹ️  This provides production stability while maintaining MCP architecture');
      }
    } else {
      logWarning('⚠️  FALLBACK DATA DETECTED');
      
      if (response.data.dataSource?.includes('fallback')) {
        logWarning('⚠️  API token may not be configured or invalid');
        logInfo('ℹ️  Please run: supabase secrets set CLOUDFLARE_API_TOKEN=****************************************');
      }
    }

    // Test 4: Data quality metrics
    console.log('\n📈 Data Quality Metrics:');
    console.log('='.repeat(50));
    
    logInfo(`Last Updated: ${response.data.lastUpdated}`);
    logInfo(`Data Source: ${response.data.dataSource}`);
    logInfo(`Response Time: ${responseTime}ms`);
    
    if (response.data.mcpMetadata) {
      logInfo(`MCP Tools: ${response.data.mcpMetadata.toolsUsed?.length || 0} tools used`);
      logInfo(`Data Freshness: ${response.data.mcpMetadata.dataFreshness}`);
    }

    // Summary
    console.log('\n' + '='.repeat(70));
    console.log('📋 VERIFICATION SUMMARY');
    console.log('='.repeat(70));

    if (isRealData) {
      logSuccess('🎉 VERIFICATION PASSED: Real Cloudflare Radar data is active!');
      logSuccess('🎉 MCP integration is working correctly with live threat intelligence');
    } else {
      logWarning('⚠️  VERIFICATION PENDING: Still using fallback data');
      logWarning('⚠️  Configure API token to enable real data integration');
    }

  } catch (error) {
    logError(`Verification failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run verification
verifyRealDataIntegration().catch(console.error);
