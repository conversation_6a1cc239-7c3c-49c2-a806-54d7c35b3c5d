import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Shield, Mail, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';
import { useCloudflareRadar } from '@/hooks/use-cloudflare-radar';

export const EmailSecurityMetrics = () => {
  const { radarData, loading, error } = useCloudflareRadar();

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !radarData?.emailSecurity) {
    return (
      <Card className="border-orange-200 bg-orange-50">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-orange-600">
            <AlertTriangle className="h-5 w-5" />
            <span>Email security data temporarily unavailable</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { emailSecurity } = radarData;

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const getAdoptionColor = (rate: number) => {
    if (rate >= 80) return 'text-green-600';
    if (rate >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getDetectionColor = (rate: number) => {
    if (rate <= 5) return 'text-green-600';
    if (rate <= 15) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2">
        <Mail className="h-5 w-5 text-blue-600" />
        <h3 className="text-lg font-semibold">Email Security Analytics</h3>
        <Badge variant="outline" className="text-xs">
          Enhanced
        </Badge>
      </div>

      {/* Email Security Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Malicious Email Detection */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Malicious Detection</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {emailSecurity.malicious.detectionRate.toFixed(1)}%
            </div>
            <div className="text-xs text-muted-foreground">
              {formatNumber(emailSecurity.malicious.detected)} detected of{' '}
              {formatNumber(emailSecurity.malicious.detected + emailSecurity.malicious.clean)} total
            </div>
            <div className="mt-2">
              <Badge 
                variant={emailSecurity.malicious.detectionRate <= 5 ? "default" : "destructive"}
                className="text-xs"
              >
                {emailSecurity.malicious.detectionRate <= 5 ? 'Low threat' : 'High threat'}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* DKIM Authentication */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">DKIM Authentication</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {emailSecurity.dkim.adoptionRate.toFixed(1)}%
            </div>
            <div className="text-xs text-muted-foreground">
              {formatNumber(emailSecurity.dkim.pass)} pass / {formatNumber(emailSecurity.dkim.fail)} fail
            </div>
            <div className="mt-2">
              <Badge 
                variant={emailSecurity.dkim.adoptionRate >= 80 ? "default" : "secondary"}
                className="text-xs"
              >
                {emailSecurity.dkim.adoptionRate >= 80 ? 'Good adoption' : 'Needs improvement'}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* SPF Validation */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">SPF Validation</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {emailSecurity.spf.adoptionRate.toFixed(1)}%
            </div>
            <div className="text-xs text-muted-foreground">
              {formatNumber(emailSecurity.spf.pass)} pass / {formatNumber(emailSecurity.spf.fail)} fail
            </div>
            <div className="mt-2">
              <Badge 
                variant={emailSecurity.spf.adoptionRate >= 80 ? "default" : "secondary"}
                className="text-xs"
              >
                {emailSecurity.spf.adoptionRate >= 80 ? 'Good adoption' : 'Needs improvement'}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Spam Detection */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Spam Detection</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {emailSecurity.spam.detectionRate.toFixed(1)}%
            </div>
            <div className="text-xs text-muted-foreground">
              {formatNumber(emailSecurity.spam.detected)} spam of{' '}
              {formatNumber(emailSecurity.spam.detected + emailSecurity.spam.clean)} total
            </div>
            <div className="mt-2">
              <Badge 
                variant={emailSecurity.spam.detectionRate <= 10 ? "default" : "destructive"}
                className="text-xs"
              >
                {emailSecurity.spam.detectionRate <= 10 ? 'Low spam' : 'High spam'}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Email Security Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Email Security Summary</CardTitle>
          <CardDescription>
            Global email authentication and threat detection metrics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-3">Authentication Protocols</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">DKIM Adoption</span>
                  <span className={`text-sm font-medium ${getAdoptionColor(emailSecurity.dkim.adoptionRate)}`}>
                    {emailSecurity.dkim.adoptionRate.toFixed(1)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">SPF Adoption</span>
                  <span className={`text-sm font-medium ${getAdoptionColor(emailSecurity.spf.adoptionRate)}`}>
                    {emailSecurity.spf.adoptionRate.toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-3">Threat Detection</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Malicious Rate</span>
                  <span className={`text-sm font-medium ${getDetectionColor(emailSecurity.malicious.detectionRate)}`}>
                    {emailSecurity.malicious.detectionRate.toFixed(1)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Spam Rate</span>
                  <span className={`text-sm font-medium ${getDetectionColor(emailSecurity.spam.detectionRate)}`}>
                    {emailSecurity.spam.detectionRate.toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
