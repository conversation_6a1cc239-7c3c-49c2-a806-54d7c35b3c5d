
import React from 'react';
import { ThreatIntelligenceWidget } from './ThreatIntelligenceWidget';
import { EmailSecurityMetrics } from './EmailSecurityMetrics';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RefreshCw, Download, Globe } from 'lucide-react';
import { useCloudflareRadar } from '@/hooks/use-cloudflare-radar';

export const ThreatIntelligenceDashboard = () => {
  const { radarData, loading, refetch } = useCloudflareRadar();

  const handleRefresh = () => {
    refetch();
  };

  const handleExportReport = () => {
    if (!radarData) return;

    const reportData = {
      generated: new Date().toISOString(),
      summary: {
        totalPhishingAttacks: radarData.phishing.total,
        totalSpoofingAttempts: radarData.spoofing.total,
        dmarcAdoptionRate: radarData.dmarc.adoptionRate,
        industryRisks: radarData.industryRisks,
        // NEW: Email security summary
        emailSecurity: radarData.emailSecurity ? {
          maliciousDetectionRate: radarData.emailSecurity.malicious.detectionRate,
          dkimAdoptionRate: radarData.emailSecurity.dkim.adoptionRate,
          spfAdoptionRate: radarData.emailSecurity.spf.adoptionRate,
          spamDetectionRate: radarData.emailSecurity.spam.detectionRate
        } : null
      },
      recommendations: [
        'Implement DMARC with strict policy to reduce spoofing attempts',
        'Conduct regular phishing awareness training for employees',
        'Monitor industry-specific threat trends for proactive defense',
        'Consider implementing additional email security measures',
        // NEW: Email security recommendations
        ...(radarData.emailSecurity ? [
          `Improve DKIM adoption (currently ${radarData.emailSecurity.dkim.adoptionRate.toFixed(1)}%)`,
          `Enhance SPF validation (currently ${radarData.emailSecurity.spf.adoptionRate.toFixed(1)}%)`,
          'Monitor malicious email detection trends for emerging threats'
        ] : [])
      ]
    };

    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `threat-intelligence-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Globe className="h-6 w-6" />
            Global Threat Intelligence
          </h2>
          <p className="text-gray-600 mt-1">
            Real-time cybersecurity insights powered by Cloudflare Radar
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={handleRefresh}
            disabled={loading}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            onClick={handleExportReport}
            disabled={!radarData}
            variant="outline"
            size="sm"
          >
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Threat Intelligence Widgets */}
      <ThreatIntelligenceWidget />

      {/* Email Security Metrics */}
      <EmailSecurityMetrics />

      {/* Additional Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Threat Intelligence Insights</CardTitle>
          <CardDescription>
            How this data enhances your security assessments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">Assessment Enhancement</h4>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Real-time threat context for risk scoring</li>
                <li>• Industry-specific risk benchmarking</li>
                <li>• Current attack trend awareness</li>
                <li>• DMARC compliance comparison</li>
                <li>• Email authentication protocol analysis</li>
                <li>• Malicious email detection insights</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Client Value</h4>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Demonstrate current threat landscape</li>
                <li>• Provide industry-relative risk positioning</li>
                <li>• Support recommendations with live data</li>
                <li>• Show urgency of security measures</li>
                <li>• Validate email security implementations</li>
                <li>• Benchmark authentication protocol adoption</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
