
import React, { useState } from "react";
import { Calendar, Shield, ArrowRight, ExternalLink, Users, Zap } from "lucide-react";
import { securityFeatures } from "@/data/security-benefits";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { CompactThreatMetrics } from "./LiveThreatMetrics";

interface HeroContentProps {
  loaded: boolean;
  animDelays: {
    badge: string;
    heading: string;
    divider: string;
    description: string;
    features: string;
    warning: string;
    cta: string;
  };
}

const HeroContent: React.FC<HeroContentProps> = ({ loaded, animDelays }) => {
  const [hoveredFeature, setHoveredFeature] = useState<number | null>(null);

  // Variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.1 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.5 } }
  };

  return (
    <motion.div 
      initial="hidden"
      animate={loaded ? "visible" : "hidden"}
      variants={containerVariants}
      className="space-y-3 xs:space-y-4 sm:space-y-5 md:space-y-6 order-2 md:order-1 md:pr-0 lg:pr-6 xl:pr-12"
    >
      {/* Badge */}
      <motion.div variants={itemVariants} className="relative">
        <span className="font-mono uppercase tracking-wider text-[10px] xs:text-xs bg-green-dark/60 text-green-200 px-3 py-1.5 rounded-full inline-flex items-center gap-1.5 shadow-sm">
          <Shield className="w-3 h-3 text-green-200" />
          SME Cybersecurity Specialists
        </span>
      </motion.div>

      {/* Service Highlight */}
      <motion.div variants={itemVariants} className="relative">
        <Link 
          to="/services"
          className="group block bg-gradient-to-r from-green-dark/30 to-green-dark/20 border border-green-muted/40 rounded-lg p-3 xs:p-4 hover:border-green-bright/60 transition-all duration-300"
        >
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 w-10 h-10 bg-green-dark/50 rounded-lg flex items-center justify-center border border-green-muted/30">
              <Users className="w-5 h-5 text-green-bright" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-[10px] xs:text-xs font-mono uppercase tracking-wider text-green-bright bg-green-dark/40 px-2 py-0.5 rounded">
                  Comprehensive Services
                </span>
                <span className="text-[10px] xs:text-xs text-white/60">Available Now</span>
              </div>
              <h3 className="text-sm xs:text-base font-semibold text-white mb-1 group-hover:text-green-bright transition-colors">
                Security Assessments, Audits & Emergency Response
              </h3>
              <p className="text-xs xs:text-sm text-white/70 mb-2">
                From vulnerability assessments to incident response - we've got you covered
              </p>
              <div className="flex items-center gap-1 text-xs text-green-bright">
                <span>Explore our services</span>
                <ExternalLink className="w-3 h-3 group-hover:translate-x-0.5 transition-transform" />
              </div>
            </div>
          </div>
        </Link>
      </motion.div>

      {/* Heading */}
      <motion.div variants={itemVariants}>
        <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold leading-tight">
          <span className="block mb-2">SME Cybersecurity</span>
          <span className="text-green-bright relative inline-block">
            Done RIGHT
            <span className="absolute -bottom-1 left-0 w-full h-[3px] bg-gradient-to-r from-green-bright to-transparent"></span>
          </span>
        </h1>
      </motion.div>

      {/* Description */}
      <motion.p 
        variants={itemVariants}
        className="text-base xs:text-lg sm:text-xl text-white/90 leading-relaxed max-w-xl"
      >
        Enterprise-grade cybersecurity designed specifically for SMEs. We deliver comprehensive security assessments, rapid incident response, and ongoing protection that <strong className="text-green-bright">actually works</strong> for your business size and budget.
      </motion.p>

      {/* Features */}
      <motion.div 
        variants={itemVariants}
        className="flex flex-wrap gap-2 xs:gap-3 items-center"
      >
        {securityFeatures.slice(0, 4).map((feature, index) => {
          const Icon = feature.icon;
          const isHovered = hoveredFeature === index;
          
          return (
            <div 
              key={index} 
              className={`flex items-center gap-2 px-3 xs:px-4 py-2 xs:py-2.5 rounded-md text-xs xs:text-sm transition-all duration-300 ${
                isHovered 
                  ? 'bg-green-dark/50 shadow-[0_0_10px_rgba(0,255,140,0.2)]' 
                  : 'bg-green-dark/20'
              }`}
              onMouseEnter={() => setHoveredFeature(index)}
              onMouseLeave={() => setHoveredFeature(null)}
            >
              <Icon className={`h-3.5 w-3.5 xs:h-4 xs:w-4 ${isHovered ? 'text-green-bright' : 'text-green-bright/80'} transition-colors duration-300`} />
              <span className={`${isHovered ? 'text-white' : 'text-white/80'} transition-colors duration-300`}>{feature.text}</span>
            </div>
          );
        })}
      </motion.div>

      {/* Value Proposition Box */}
      <motion.div
        variants={itemVariants}
        className="flex items-start xs:items-center gap-3 p-3 xs:p-4 bg-gradient-to-r from-blue-900/30 to-green-900/30 border border-green-500/30 rounded-md text-sm xs:text-base max-w-xl shadow-lg"
      >
        <Zap className="h-5 w-5 xs:h-6 xs:w-6 text-green-400 flex-shrink-0 mt-0.5 xs:mt-0" />
        <p className="text-white/90">
          <strong>Data-Driven Security:</strong> We leverage real-time global threat intelligence to protect SMEs with enterprise-grade security. See live threat data below and get started in just 2-4 weeks.
        </p>
      </motion.div>

      {/* Live Threat Intelligence Metrics */}
      <motion.div variants={itemVariants}>
        <CompactThreatMetrics delay={parseFloat(animDelays.warning)} />
      </motion.div>

      {/* Enhanced CTA Buttons */}
      <motion.div
        variants={itemVariants}
        className="pt-4 xs:pt-6 flex flex-col xs:flex-row flex-wrap gap-4"
      >
        <Link
          to="/phishing-assessment"
          className="group relative overflow-hidden bg-gradient-to-r from-green-500 to-green-600 text-white font-bold rounded-md inline-flex items-center px-6 py-3 text-base shadow-lg hover:from-green-400 hover:to-green-500 transition-all duration-300"
        >
          <span className="relative z-10 flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Free Security Assessment
          </span>
        </Link>
        
        <Link
          to="/services"
          className="group relative overflow-hidden bg-green-bright text-black font-medium rounded-md inline-flex items-center px-5 py-3 text-base hover:bg-green-muted transition-colors"
        >
          <span className="relative z-10 flex items-center gap-2">
            <Users className="w-5 h-5" />
            <span>View All Services</span>
          </span>
        </Link>
        
        <a
          href="https://calendly.com/blackveil"
          target="_blank"
          rel="noopener noreferrer"
          className="group relative inline-flex items-center bg-transparent border border-green-400/50 text-green-200 hover:text-green-100 hover:border-green-300 px-5 py-3 rounded-md font-medium text-base transition-all duration-300"
        >
          <Calendar className="w-5 h-5 mr-2" />
          <span>Book Consultation</span>
          <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
        </a>
      </motion.div>
    </motion.div>
  );
};

export default React.memo(HeroContent);
