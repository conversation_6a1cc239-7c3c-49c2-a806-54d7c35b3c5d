import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Shield, Mail, AlertTriangle, TrendingUp, Activity, Globe } from 'lucide-react';
import { useCloudflareRadar } from '@/hooks/use-cloudflare-radar';

interface AnimatedCounterProps {
  value: number;
  duration?: number;
  suffix?: string;
  prefix?: string;
  decimals?: number;
}

const AnimatedCounter: React.FC<AnimatedCounterProps> = ({ 
  value, 
  duration = 2000, 
  suffix = '', 
  prefix = '', 
  decimals = 0 
}) => {
  const [displayValue, setDisplayValue] = useState(0);

  useEffect(() => {
    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentValue = value * easeOutQuart;
      
      setDisplayValue(currentValue);

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [value, duration]);

  const formatValue = (val: number) => {
    if (val >= 1000000) {
      return (val / 1000000).toFixed(decimals) + 'M';
    }
    if (val >= 1000) {
      return (val / 1000).toFixed(decimals) + 'K';
    }
    return val.toFixed(decimals);
  };

  return (
    <span className="font-mono">
      {prefix}{formatValue(displayValue)}{suffix}
    </span>
  );
};

interface MetricCardProps {
  icon: React.ReactNode;
  label: string;
  value: number;
  suffix?: string;
  trend?: number;
  isLive?: boolean;
  delay?: number;
}

const MetricCard: React.FC<MetricCardProps> = ({ 
  icon, 
  label, 
  value, 
  suffix = '', 
  trend, 
  isLive = false,
  delay = 0 
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay }}
      className="relative group"
    >
      <div className="relative bg-black-soft/60 backdrop-blur-sm border border-green-muted/30 rounded-lg p-4 
                      hover:border-green-muted/50 transition-all duration-300 group-hover:shadow-[0_0_20px_rgba(0,255,140,0.1)]">
        
        {/* Live indicator */}
        {isLive && (
          <div className="absolute top-2 right-2 flex items-center gap-1">
            <motion.div
              className="w-2 h-2 bg-green-bright rounded-full"
              animate={{ opacity: [1, 0.3, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
            <span className="text-xs text-green-bright font-mono">LIVE</span>
          </div>
        )}

        {/* Icon */}
        <div className="flex items-center justify-between mb-3">
          <div className="p-2 bg-green-dark/40 rounded-md">
            {icon}
          </div>
          {trend !== undefined && (
            <div className={`flex items-center gap-1 text-xs ${trend >= 0 ? 'text-red-400' : 'text-green-400'}`}>
              <TrendingUp className={`w-3 h-3 ${trend < 0 ? 'rotate-180' : ''}`} />
              <span>{Math.abs(trend).toFixed(1)}%</span>
            </div>
          )}
        </div>

        {/* Value */}
        <div className="mb-2">
          <div className="text-2xl font-bold text-white">
            <AnimatedCounter value={value} suffix={suffix} decimals={suffix === '%' ? 1 : 0} />
          </div>
        </div>

        {/* Label */}
        <div className="text-sm text-white/70">{label}</div>

        {/* Hover glow effect */}
        <div className="absolute inset-0 bg-green-bright/5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
      </div>
    </motion.div>
  );
};

interface LiveThreatMetricsProps {
  className?: string;
  delay?: number;
}

// Compact version for hero section
export const CompactThreatMetrics: React.FC<LiveThreatMetricsProps> = ({
  className = '',
  delay = 0
}) => {
  const { radarData, loading, hasRealData } = useCloudflareRadar();

  if (loading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay }}
        className={`bg-black-soft/40 backdrop-blur-sm border border-green-muted/30 rounded-lg p-4 ${className}`}
      >
        <div className="flex items-center gap-2 mb-3">
          <Activity className="w-4 h-4 text-green-bright animate-pulse" />
          <span className="text-sm text-green-bright font-mono">Loading live data...</span>
        </div>
        <div className="grid grid-cols-2 gap-4">
          {[...Array(2)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="h-6 bg-green-muted/20 rounded w-3/4 mb-1"></div>
              <div className="h-3 bg-green-muted/20 rounded w-full"></div>
            </div>
          ))}
        </div>
      </motion.div>
    );
  }

  if (!radarData) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay }}
      className={`bg-black-soft/40 backdrop-blur-sm border border-green-muted/30 rounded-lg p-4 hover:border-green-muted/50 transition-all duration-300 hover:shadow-[0_0_20px_rgba(0,255,140,0.1)] ${className}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Activity className="w-4 h-4 text-green-bright" />
          <span className="text-sm text-green-bright font-mono">
            {hasRealData ? 'LIVE THREAT DATA' : 'THREAT PREVIEW'}
          </span>
        </div>

        {hasRealData && (
          <motion.div
            className="w-2 h-2 bg-green-bright rounded-full"
            animate={{ opacity: [1, 0.3, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        )}
      </div>

      {/* Compact Metrics */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <div className="text-lg font-bold text-white font-mono">
            <AnimatedCounter value={radarData.phishing.total} />
          </div>
          <div className="text-xs text-white/70">Global Phishing Attacks</div>
        </div>

        <div>
          <div className="text-lg font-bold text-white font-mono">
            <AnimatedCounter
              value={radarData.emailSecurity.malicious.detectionRate}
              suffix="%"
              decimals={1}
            />
          </div>
          <div className="text-xs text-white/70">Malicious Email Rate</div>
        </div>
      </div>

      {/* Data Source */}
      <div className="text-xs text-white/50 text-center mt-3 pt-2 border-t border-green-muted/20">
        {hasRealData ? 'Powered by Cloudflare Radar' : 'Demo data available'}
      </div>
    </motion.div>
  );
};

export const LiveThreatMetrics: React.FC<LiveThreatMetricsProps> = ({
  className = '',
  delay = 0
}) => {
  const { radarData, loading, hasRealData } = useCloudflareRadar();

  if (loading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay }}
        className={`space-y-4 ${className}`}
      >
        <div className="flex items-center gap-2 mb-4">
          <Activity className="w-5 h-5 text-green-bright animate-pulse" />
          <span className="text-sm text-green-bright font-mono">Loading threat intelligence...</span>
        </div>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-black-soft/60 border border-green-muted/30 rounded-lg p-4 animate-pulse">
              <div className="h-4 bg-green-muted/20 rounded w-3/4 mb-3"></div>
              <div className="h-8 bg-green-muted/20 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-green-muted/20 rounded w-full"></div>
            </div>
          ))}
        </div>
      </motion.div>
    );
  }

  if (!radarData) {
    return null;
  }

  const metrics = [
    {
      icon: <AlertTriangle className="w-5 h-5 text-red-400" />,
      label: 'Global Phishing Attacks',
      value: radarData.phishing.total,
      trend: radarData.phishing.trend,
      isLive: hasRealData
    },
    {
      icon: <Mail className="w-5 h-5 text-orange-400" />,
      label: 'Malicious Email Rate',
      value: radarData.emailSecurity.malicious.detectionRate,
      suffix: '%',
      isLive: hasRealData
    },
    {
      icon: <Shield className="w-5 h-5 text-blue-400" />,
      label: 'DMARC Adoption',
      value: radarData.emailSecurity.dkim.adoptionRate,
      suffix: '%',
      trend: radarData.dmarc.trend,
      isLive: hasRealData
    },
    {
      icon: <Globe className="w-5 h-5 text-green-400" />,
      label: 'Email Spoofing Attempts',
      value: radarData.spoofing.total,
      trend: radarData.spoofing.trend,
      isLive: hasRealData
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay }}
      className={`space-y-4 ${className}`}
    >
      {/* Header */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: delay + 0.2 }}
        className="flex items-center justify-between"
      >
        <div className="flex items-center gap-2">
          <Activity className="w-5 h-5 text-green-bright" />
          <span className="text-sm text-green-bright font-mono">
            {hasRealData ? 'LIVE THREAT INTELLIGENCE' : 'THREAT INTELLIGENCE PREVIEW'}
          </span>
        </div>
        
        {hasRealData && (
          <div className="flex items-center gap-2 text-xs text-white/60">
            <motion.div
              className="w-2 h-2 bg-green-bright rounded-full"
              animate={{ opacity: [1, 0.3, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
            <span>Updated {new Date(radarData.lastUpdated).toLocaleTimeString()}</span>
          </div>
        )}
      </motion.div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
        {metrics.map((metric, index) => (
          <MetricCard
            key={index}
            icon={metric.icon}
            label={metric.label}
            value={metric.value}
            suffix={metric.suffix}
            trend={metric.trend}
            isLive={metric.isLive}
            delay={delay + 0.3 + (index * 0.1)}
          />
        ))}
      </div>

      {/* Data Source Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: delay + 1 }}
        className="text-xs text-white/50 text-center"
      >
        {hasRealData ? (
          <span>Powered by Cloudflare Radar • Real-time global threat data</span>
        ) : (
          <span>Demo data • Real-time integration available</span>
        )}
      </motion.div>
    </motion.div>
  );
};
